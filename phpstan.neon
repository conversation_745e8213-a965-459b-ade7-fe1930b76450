parameters:
    level: max
    paths:
        - src
    excludePaths:
        - tests
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    inferPrivatePropertyTypeFromConstructor: true
    checkUninitializedProperties: true
    reportUnmatchedIgnoredErrors: false
    checkUnionTypes: true
    checkExplicitMixed: true
    checkMissingVarTagType: true
    checkMissingIterableKeyType: true
    checkFunctionNameCase: true
    checkClassNameCase: true
    checkFunctionSignatureMismatch: true
    checkPhpDocSignatureMismatch: true
    checkThisOnly: true
    checkAlwaysTrueCheckTypeFunctionCall: true
    checkAlwaysTrueCheckTypeMethodCall: true
    checkAlwaysTrueCheckTypePropertyFetch: true
    checkAlwaysTrueCheckTypeStaticMethodCall: true
    checkAlwaysTrueCheckTypeStaticPropertyFetch: true
    checkAlwaysTrueCheckTypeConstantFetch: true
    checkAlwaysTrueCheckTypeFunctionParameter: true
    checkAlwaysTrueCheckTypeMethodParameter: true
    checkAlwaysTrueCheckTypePropertyParameter: true
    checkAlwaysTrueCheckTypeStaticMethodParameter: true
    checkAlwaysTrueCheckTypeStaticPropertyParameter: true
    checkAlwaysTrueCheckTypeConstantParameter: true
    checkAlwaysTrueCheckTypeFunctionReturn: true
    checkAlwaysTrueCheckTypeMethodReturn: true
    checkAlwaysTrueCheckTypePropertyReturn: true
    checkAlwaysTrueCheckTypeStaticMethodReturn: true
    checkAlwaysTrueCheckTypeStaticPropertyReturn: true
    checkAlwaysTrueCheckTypeConstantReturn: true
    checkAlwaysTrueCheckTypeFunctionThrows: true
    checkAlwaysTrueCheckTypeMethodThrows: true
    checkAlwaysTrueCheckTypePropertyThrows: true
    checkAlwaysTrueCheckTypeStaticMethodThrows: true
    checkAlwaysTrueCheckTypeStaticPropertyThrows: true
    checkAlwaysTrueCheckTypeConstantThrows: true
    checkAlwaysTrueCheckTypeFunctionCall: true
    checkAlwaysTrueCheckTypeMethodCall: true
    checkAlwaysTrueCheckTypePropertyFetch: true
    checkAlwaysTrueCheckTypeStaticMethodCall: true
    checkAlwaysTrueCheckTypeStaticPropertyFetch: true
    checkAlwaysTrueCheckTypeConstantFetch: true
    checkAlwaysTrueCheckTypeFunctionParameter: true
    checkAlwaysTrueCheckTypeMethodParameter: true
    checkAlwaysTrueCheckTypePropertyParameter: true
    checkAlwaysTrueCheckTypeStaticMethodParameter: true
    checkAlwaysTrueCheckTypeStaticPropertyParameter: true
    checkAlwaysTrueCheckTypeConstantParameter: true
    checkAlwaysTrueCheckTypeFunctionReturn: true
    checkAlwaysTrueCheckTypeMethodReturn: true
    checkAlwaysTrueCheckTypePropertyReturn: true
    checkAlwaysTrueCheckTypeStaticMethodReturn: true
    checkAlwaysTrueCheckTypeStaticPropertyReturn: true
    checkAlwaysTrueCheckTypeConstantReturn: true
    checkAlwaysTrueCheckTypeFunctionThrows: true
    checkAlwaysTrueCheckTypeMethodThrows: true
    checkAlwaysTrueCheckTypePropertyThrows: true
    checkAlwaysTrueCheckTypeStaticMethodThrows: true
    checkAlwaysTrueCheckTypeStaticPropertyThrows: true
    checkAlwaysTrueCheckTypeConstantThrows: true
    checkAlwaysTrueCheckTypeFunctionCall: true
    checkAlwaysTrueCheckTypeMethodCall: true
    checkAlwaysTrueCheckTypePropertyFetch: true
    checkAlwaysTrueCheckTypeStaticMethodCall: true
    checkAlwaysTrueCheckTypeStaticPropertyFetch: true
    checkAlwaysTrueCheckTypeConstantFetch: true
    checkAlwaysTrueCheckTypeFunctionParameter: true
    checkAlwaysTrueCheckTypeMethodParameter: true
    checkAlwaysTrueCheckTypePropertyParameter: true
    checkAlwaysTrueCheckTypeStaticMethodParameter: true
    checkAlwaysTrueCheckTypeStaticPropertyParameter: true
    checkAlwaysTrueCheckTypeConstantParameter: true
    checkAlwaysTrueCheckTypeFunctionReturn: true
    checkAlwaysTrueCheckTypeMethodReturn: true
    checkAlwaysTrueCheckTypePropertyReturn: true
    checkAlwaysTrueCheckTypeStaticMethodReturn: true
    checkAlwaysTrueCheckTypeStaticPropertyReturn: true
    checkAlwaysTrueCheckTypeConstantReturn: true
    checkAlwaysTrueCheckTypeFunctionThrows: true
    checkAlwaysTrueCheckTypeMethodThrows: true
    checkAlwaysTrueCheckTypePropertyThrows: true
    checkAlwaysTrueCheckTypeStaticMethodThrows: true
    checkAlwaysTrueCheckTypeStaticPropertyThrows: true
    checkAlwaysTrueCheckTypeConstantThrows: true
    checkAlwaysTrueCheckTypeFunctionCall: true
    checkAlwaysTrueCheckTypeMethodCall: true
    checkAlwaysTrueCheckTypePropertyFetch: true
    checkAlwaysTrueCheckTypeStaticMethodCall: true
    checkAlwaysTrueCheckTypeStaticPropertyFetch: true
    checkAlwaysTrueCheckTypeConstantFetch: true
    checkAlwaysTrueCheckTypeFunctionParameter: true
    checkAlwaysTrueCheckTypeMethodParameter: true
    checkAlwaysTrueCheckTypePropertyParameter: true
    checkAlwaysTrueCheckTypeStaticMethodParameter: true
    checkAlwaysTrueCheckTypeStaticPropertyParameter: true
    checkAlwaysTrueCheckTypeConstantParameter: true
    checkAlwaysTrueCheckTypeFunctionReturn: true
    checkAlwaysTrueCheckTypeMethodReturn: true
    checkAlwaysTrueCheckTypePropertyReturn: true
    checkAlwaysTrueCheckTypeStaticMethodReturn: true
    checkAlwaysTrueCheckTypeStaticPropertyReturn: true
    checkAlwaysTrueCheckTypeConstantReturn: true
    checkAlwaysTrueCheckTypeFunctionThrows: true
    checkAlwaysTrueCheckTypeMethodThrows: true
    checkAlwaysTrueCheckTypePropertyThrows: true
    checkAlwaysTrueCheckTypeStaticMethodThrows: true
    checkAlwaysTrueCheckTypeStaticPropertyThrows: true
    checkAlwaysTrueCheckTypeConstantThrows: true
    checkAlwaysTrueCheckTypeFunctionCall: true
    checkAlwaysTrueCheckTypeMethodCall: true
    checkAlwaysTrueCheckTypePropertyFetch: true
    checkAlwaysTrueCheckTypeStaticMethodCall: true
    checkAlwaysTrueCheckTypeStaticPropertyFetch: true
    checkAlwaysTrueCheckTypeConstantFetch: true
    checkAlwaysTrueCheckTypeFunctionParameter: true
    checkAlwaysTrueCheckTypeMethodParameter: true
    checkAlwaysTrueCheckTypePropertyParameter: true
    checkAlwaysTrueCheckTypeStaticMethodParameter: true
    checkAlwaysTrueCheckTypeStaticPropertyParameter: true
    checkAlwaysTrueCheckTypeConstantParameter: true
    checkAlwaysTrueCheckTypeFunctionReturn: true
    checkAlwaysTrueCheckTypeMethodReturn: true
    checkAlwaysTrueCheckTypePropertyReturn: true
    checkAlwaysTrueCheckTypeStaticMethodReturn: true
    checkAlwaysTrueCheckTypeStaticPropertyReturn: true
    checkAlwaysTrueCheckTypeConstantReturn: true
    checkAlwaysTrueCheckTypeFunctionThrows: true
    checkAlwaysTrueCheckTypeMethodThrows: true
    checkAlwaysTrueCheckTypePropertyThrows: true
    checkAlwaysTrueCheckTypeStaticMethodThrows: true
    checkAlwaysTrueCheckTypeStaticPropertyThrows: true
    checkAlwaysTrueCheckTypeConstantThrows: true
