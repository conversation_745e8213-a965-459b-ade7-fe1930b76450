{"name": "imponeer/editor-contracts", "description": "Interfaces for building PHP classes that lets to easier make web editor integrations in your content management systems", "type": "library", "license": "MIT", "autoload": {"psr-4": {"Imponeer\\Contracts\\Editor\\": "src/"}}, "require": {"php": ">=8.3"}, "require-dev": {"squizlabs/php_codesniffer": "^3.9", "phpstan/phpstan": "^2.0"}, "scripts": {"phpcs": "phpcs", "phpcbf": "phpcbf", "phpstan": "phpstan analyse --memory-limit=1G"}}